#!/usr/bin/env node

/**
 * Complete Blog Image Workflow
 * 
 * This script demonstrates the complete workflow for generating blog images using MCP tools:
 * 1. Generate images using Replicate Flux via MCP
 * 2. Upload images to Cloudflare R2
 * 3. Update Supabase blog table with custom domain URLs
 */

const { createClient } = require('@supabase/supabase-js');
const AWS = require('aws-sdk');

// Configuration
const SUPABASE_CONFIG = {
  url: 'https://rhcwssrsdwakputbbpuf.supabase.co',
  key: 'sb_publishable_oIFU2557bCSsMHmplaGSeg_VkTmMr0K',
  tableName: 'findcaraccidentattorneys-blog'
};

const CLOUDFLARE_R2_CONFIG = {
  endpoint: 'https://fa488c806af1dafb4525e54efb42f2ea.r2.cloudflarestorage.com',
  bucketName: 'directory-findcaraccidentlawyers-live-040925',
  region: 'auto',
  accessKeyId: 'c789b94077ced966f3c5491e846fefa3',
  secretAccessKey: '****************************************************************',
  publicUrlBase: 'https://images.findcaraccidentlawyers.org'
};

// Initialize clients
const supabase = createClient(SUPABASE_CONFIG.url, SUPABASE_CONFIG.key);

function initializeR2Client() {
  return new AWS.S3({
    endpoint: CLOUDFLARE_R2_CONFIG.endpoint,
    accessKeyId: CLOUDFLARE_R2_CONFIG.accessKeyId,
    secretAccessKey: CLOUDFLARE_R2_CONFIG.secretAccessKey,
    region: CLOUDFLARE_R2_CONFIG.region,
    signatureVersion: 'v4',
    s3ForcePathStyle: true
  });
}

// Upload image to R2 and return custom domain URL
async function uploadImageToR2(imageUrl, filename) {
  console.log(`☁️ Uploading ${filename} to R2...`);
  
  try {
    // Download image
    const response = await fetch(imageUrl);
    if (!response.ok) {
      throw new Error(`Failed to download image: ${response.status}`);
    }
    
    const imageBuffer = Buffer.from(await response.arrayBuffer());
    const r2Client = initializeR2Client();
    
    // Upload to R2
    const uploadParams = {
      Bucket: CLOUDFLARE_R2_CONFIG.bucketName,
      Key: filename,
      Body: imageBuffer,
      ContentType: 'image/webp',
      ACL: 'public-read'
    };
    
    await r2Client.upload(uploadParams).promise();
    
    // Return custom domain URL
    const customUrl = `${CLOUDFLARE_R2_CONFIG.publicUrlBase}/${filename}`;
    console.log(`✅ Uploaded successfully: ${customUrl}`);
    
    return customUrl;
    
  } catch (error) {
    console.error(`❌ Upload failed: ${error.message}`);
    return null;
  }
}

// Update blog post with image URL
async function updateBlogPost(blogId, imageUrl) {
  console.log(`💾 Updating blog post ${blogId}...`);
  
  try {
    const { error } = await supabase
      .from(SUPABASE_CONFIG.tableName)
      .update({ 
        featured_image: imageUrl,
        updated_at: new Date().toISOString()
      })
      .eq('id', blogId);
    
    if (error) {
      throw error;
    }
    
    console.log('✅ Blog post updated successfully');
    return true;
    
  } catch (error) {
    console.error(`❌ Update failed: ${error.message}`);
    return false;
  }
}

// Complete workflow for one blog post
async function processOneBlogPost(blogPost, imageUrl) {
  console.log(`\n🔄 Processing: "${blogPost.title}"`);
  
  // Generate filename
  const timestamp = Date.now();
  const filename = `blog-images/${blogPost.slug}-${timestamp}.webp`;
  
  // Upload to R2
  const customUrl = await uploadImageToR2(imageUrl, filename);
  if (!customUrl) {
    return false;
  }
  
  // Update database
  const success = await updateBlogPost(blogPost.id, customUrl);
  
  if (success) {
    console.log(`✅ Successfully processed: "${blogPost.title}"`);
    console.log(`🔗 Image URL: ${customUrl}`);
  }
  
  return success;
}

// Demonstration function
async function demonstrateWorkflow() {
  console.log('🚀 Blog Image Workflow Demonstration\n');
  
  // Get blog posts without images
  const { data: blogPosts, error } = await supabase
    .from(SUPABASE_CONFIG.tableName)
    .select('*')
    .or('featured_image.is.null,featured_image.eq./placeholder.jpg')
    .limit(1); // Process just one for demonstration
  
  if (error) {
    console.error('❌ Error fetching blog posts:', error);
    return;
  }
  
  if (blogPosts.length === 0) {
    console.log('✅ No blog posts need images');
    return;
  }
  
  const blogPost = blogPosts[0];
  console.log(`📝 Demo blog post: "${blogPost.title}"`);
  
  // For demonstration, we'll use the image we generated earlier
  const demoImageUrl = 'https://replicate.delivery/xezq/mnmIX0uG8lrcC5SVhTnwZLWnkLk0uI6N4GSMfhtWdEG0p6oKA/out-0.webp';
  
  console.log('🎨 Using pre-generated image for demonstration');
  console.log(`🔗 Source image: ${demoImageUrl}`);
  
  // Process the blog post
  const success = await processOneBlogPost(blogPost, demoImageUrl);
  
  if (success) {
    console.log('\n🎉 Workflow completed successfully!');
    
    // Verify the update
    const { data: updatedPost } = await supabase
      .from(SUPABASE_CONFIG.tableName)
      .select('title, featured_image')
      .eq('id', blogPost.id)
      .single();
    
    if (updatedPost) {
      console.log('\n📊 Verification:');
      console.log(`   Title: ${updatedPost.title}`);
      console.log(`   Image: ${updatedPost.featured_image}`);
    }
  } else {
    console.log('\n❌ Workflow failed');
  }
}

// Manual processing function for MCP environment
async function manualMCPWorkflow() {
  console.log('📋 Manual MCP Workflow Instructions\n');
  
  // Get all blog posts without images
  const { data: blogPosts, error } = await supabase
    .from(SUPABASE_CONFIG.tableName)
    .select('*')
    .or('featured_image.is.null,featured_image.eq./placeholder.jpg')
    .order('created_at', { ascending: false });
  
  if (error) {
    console.error('❌ Error fetching blog posts:', error);
    return;
  }
  
  console.log(`Found ${blogPosts.length} blog posts needing images\n`);
  
  blogPosts.forEach((post, index) => {
    console.log(`--- Blog Post ${index + 1} ---`);
    console.log(`ID: ${post.id}`);
    console.log(`Title: ${post.title}`);
    console.log(`Slug: ${post.slug}`);
    
    // Generate appropriate prompt based on content
    let prompt = 'Mobile phone photo of professional law office, natural lighting, slightly casual angle, realistic business environment, authentic atmosphere';
    
    const text = (post.title + ' ' + post.slug + ' ' + (post.excerpt || '')).toLowerCase();
    
    if (text.includes('car accident') || text.includes('auto accident')) {
      prompt = 'Mobile phone photo of car accident scene, slightly blurry, natural lighting, emergency vehicles in background, realistic street setting, amateur photography style, slightly tilted angle';
    } else if (text.includes('personal injury') || text.includes('injury')) {
      prompt = 'Candid mobile phone photo of person with visible injury, hospital or medical setting, natural lighting, slightly grainy quality, authentic documentary style';
    } else if (text.includes('attorney') || text.includes('lawyer')) {
      prompt = 'Mobile phone photo of attorney consultation, office setting, natural lighting through window, slightly informal angle, realistic business meeting atmosphere';
    } else if (text.includes('insurance') || text.includes('claim')) {
      prompt = 'Mobile phone photo of insurance paperwork and damaged vehicle, natural outdoor lighting, realistic documentation style, slightly amateur composition';
    }
    
    console.log(`Prompt: ${prompt}`);
    console.log(`\n🎨 MCP Command:`);
    console.log(`generate_image_replicate-flux-mcp with parameters:`);
    console.log(JSON.stringify({
      prompt: prompt,
      go_fast: true,
      megapixels: "1",
      num_outputs: 1,
      aspect_ratio: "16:9",
      output_format: "webp",
      output_quality: 80,
      num_inference_steps: 4
    }, null, 2));
    
    console.log(`\n📁 Suggested filename: blog-images/${post.slug}-${Date.now()}.webp`);
    console.log(`🔗 Final URL format: https://images.findcaraccidentlawyers.org/blog-images/${post.slug}-TIMESTAMP.webp\n`);
  });
  
  console.log('💡 Workflow Steps:');
  console.log('1. Use the MCP commands above to generate images');
  console.log('2. Upload each image to R2 with the suggested filename');
  console.log('3. Update each blog post with the final URL');
  console.log('4. Use the processOneBlogPost function for automated upload/update');
}

// Main function
async function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--demo')) {
    await demonstrateWorkflow();
  } else if (args.includes('--manual')) {
    await manualMCPWorkflow();
  } else {
    console.log('🚀 Complete Blog Image Workflow\n');
    console.log('Usage:');
    console.log('  node complete-blog-image-workflow.js --demo    # Run demonstration');
    console.log('  node complete-blog-image-workflow.js --manual  # Show manual MCP workflow');
    console.log('\nThis script demonstrates the complete workflow for:');
    console.log('✅ Generating images with Replicate Flux via MCP');
    console.log('✅ Uploading to Cloudflare R2');
    console.log('✅ Updating Supabase with custom domain URLs');
  }
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { processOneBlogPost, uploadImageToR2, updateBlogPost };
