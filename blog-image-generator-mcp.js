#!/usr/bin/env node

/**
 * Blog Image Generator Script (MCP Version)
 * 
 * This script generates images for blog posts using Replicate Flux via MCP tools and stores them in Cloudflare R2.
 * It reads blog posts from the database and creates relevant featured images with mobile-phone-like quality.
 * 
 * Usage: This script is designed to be run in an MCP-enabled environment
 * 
 * Configuration:
 * - Update SUPABASE_CONFIG with your project details
 * - Configure CLOUDFLARE_R2_CONFIG with your R2 credentials
 * - Uses Replicate Flux Schnell model via MCP tools
 */

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');
const AWS = require('aws-sdk');

// ===== CONFIGURATION SECTION =====

const SUPABASE_CONFIG = {
  url: 'https://rhcwssrsdwakputbbpuf.supabase.co',
  key: 'sb_publishable_oIFU2557bCSsMHmplaGSeg_VkTmMr0K',
  tableName: 'findcaraccidentattorneys-blog'
};

const REPLICATE_CONFIG = {
  model: 'black-forest-labs/flux-schnell',
  // MCP tools handle the API communication
};

const CLOUDFLARE_R2_CONFIG = {
  endpoint: 'https://fa488c806af1dafb4525e54efb42f2ea.r2.cloudflarestorage.com',
  bucketName: 'directory-findcaraccidentlawyers-live-040925',
  region: 'auto',
  accessKeyId: 'c789b94077ced966f3c5491e846fefa3',
  secretAccessKey: '****************************************************************',
  // Custom domain for public access
  publicUrlBase: 'https://images.findcaraccidentlawyers.org'
};

// ===== IMAGE GENERATION PROMPTS =====

const MOBILE_STYLE_PROMPTS = {
  'car accident': 'Mobile phone photo of car accident scene, slightly blurry, natural lighting, emergency vehicles in background, realistic street setting, amateur photography style, slightly tilted angle',
  'personal injury': 'Candid mobile phone photo of person with visible injury, hospital or medical setting, natural lighting, slightly grainy quality, authentic documentary style',
  'attorney meeting': 'Mobile phone photo of attorney consultation, office setting, natural lighting through window, slightly informal angle, realistic business meeting atmosphere',
  'legal documents': 'Mobile phone photo of legal papers on desk, natural overhead lighting, slightly casual angle, realistic office environment, papers scattered naturally',
  'insurance claim': 'Mobile phone photo of insurance paperwork and damaged vehicle, natural outdoor lighting, realistic documentation style, slightly amateur composition',
  'courtroom': 'Mobile phone photo from back of courtroom, natural lighting, slightly distant perspective, realistic legal proceeding atmosphere',
  'medical treatment': 'Mobile phone photo of medical examination or treatment, hospital setting, natural fluorescent lighting, authentic healthcare environment',
  'settlement meeting': 'Candid mobile phone photo of handshake or document signing, office setting, natural window lighting, realistic business conclusion',
  'traffic scene': 'Mobile phone photo of busy traffic or intersection, natural daylight, slightly tilted perspective, realistic urban environment',
  'legal consultation': 'Mobile phone photo of lawyer explaining documents to client, natural office lighting, authentic consultation atmosphere',
  'default': 'Mobile phone photo of professional law office, natural lighting, slightly casual angle, realistic business environment, authentic atmosphere'
};

// ===== UTILITY FUNCTIONS =====

function getImagePrompt(title, slug, excerpt) {
  const text = (title + ' ' + slug + ' ' + (excerpt || '')).toLowerCase();
  
  if (text.includes('car accident') || text.includes('auto accident') || text.includes('vehicle accident')) {
    return MOBILE_STYLE_PROMPTS['car accident'];
  }
  if (text.includes('personal injury') || text.includes('injury claim') || text.includes('injured')) {
    return MOBILE_STYLE_PROMPTS['personal injury'];
  }
  if (text.includes('attorney') || text.includes('lawyer') || text.includes('legal representation')) {
    return MOBILE_STYLE_PROMPTS['attorney meeting'];
  }
  if (text.includes('insurance') || text.includes('claim') || text.includes('coverage')) {
    return MOBILE_STYLE_PROMPTS['insurance claim'];
  }
  if (text.includes('settlement') || text.includes('compensation') || text.includes('damages')) {
    return MOBILE_STYLE_PROMPTS['settlement meeting'];
  }
  if (text.includes('medical') || text.includes('treatment') || text.includes('doctor') || text.includes('hospital')) {
    return MOBILE_STYLE_PROMPTS['medical treatment'];
  }
  if (text.includes('court') || text.includes('trial') || text.includes('lawsuit')) {
    return MOBILE_STYLE_PROMPTS['courtroom'];
  }
  if (text.includes('traffic') || text.includes('road') || text.includes('intersection')) {
    return MOBILE_STYLE_PROMPTS['traffic scene'];
  }
  if (text.includes('legal advice') || text.includes('consultation') || text.includes('legal help')) {
    return MOBILE_STYLE_PROMPTS['legal consultation'];
  }
  if (text.includes('document') || text.includes('paperwork') || text.includes('legal forms')) {
    return MOBILE_STYLE_PROMPTS['legal documents'];
  }
  
  return MOBILE_STYLE_PROMPTS.default;
}

function generateImageFilename(blogSlug) {
  const timestamp = Date.now();
  return `blog-images/${blogSlug}-${timestamp}.webp`;
}

async function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// ===== MCP IMAGE GENERATION FUNCTIONS =====

function createMCPImageRequest(prompt) {
  return {
    tool: 'generate_image_replicate-flux-mcp',
    parameters: {
      prompt: prompt,
      go_fast: true,
      megapixels: "1",
      num_outputs: 1,
      aspect_ratio: "16:9",
      output_format: "webp",
      output_quality: 80,
      num_inference_steps: 4
    }
  };
}

// This function provides the MCP request structure for image generation
function generateMCPImageRequest(prompt) {
  console.log(`🎨 Creating MCP image generation request: "${prompt.substring(0, 50)}..."`);
  console.log(`🚀 Using model: ${REPLICATE_CONFIG.model}`);
  
  const mcpRequest = createMCPImageRequest(prompt);
  
  console.log('📋 MCP Request Structure:');
  console.log(JSON.stringify(mcpRequest, null, 2));
  
  return mcpRequest;
}

// ===== CLOUDFLARE R2 FUNCTIONS =====

function initializeR2Client() {
  return new AWS.S3({
    endpoint: CLOUDFLARE_R2_CONFIG.endpoint,
    accessKeyId: CLOUDFLARE_R2_CONFIG.accessKeyId,
    secretAccessKey: CLOUDFLARE_R2_CONFIG.secretAccessKey,
    region: CLOUDFLARE_R2_CONFIG.region,
    signatureVersion: 'v4',
    s3ForcePathStyle: true
  });
}

async function uploadToCloudflareR2(imageUrl, filename) {
  console.log(`☁️ Uploading image to Cloudflare R2: ${filename}`);

  try {
    const imageResponse = await fetch(imageUrl);
    if (!imageResponse.ok) {
      throw new Error(`Failed to download image: ${imageResponse.status}`);
    }

    const imageBuffer = Buffer.from(await imageResponse.arrayBuffer());
    const r2Client = initializeR2Client();

    const uploadParams = {
      Bucket: CLOUDFLARE_R2_CONFIG.bucketName,
      Key: filename,
      Body: imageBuffer,
      ContentType: 'image/webp',
      ACL: 'public-read'
    };

    console.log(`📤 Uploading to R2 bucket: ${CLOUDFLARE_R2_CONFIG.bucketName}`);
    const result = await r2Client.upload(uploadParams).promise();

    // Use custom domain URL
    const publicUrl = `${CLOUDFLARE_R2_CONFIG.publicUrlBase}/${filename}`;
    
    console.log(`✅ Successfully uploaded to R2: ${result.Location}`);
    console.log(`🔗 Public URL: ${publicUrl}`);

    return publicUrl;

  } catch (error) {
    console.error(`❌ Error uploading to R2: ${error.message}`);
    return null;
  }
}

// ===== DATABASE FUNCTIONS =====

async function getBlogPostsWithoutImages(supabase) {
  console.log('📋 Fetching blog posts without featured images...');
  
  const { data, error } = await supabase
    .from(SUPABASE_CONFIG.tableName)
    .select('*')
    .or('featured_image.is.null,featured_image.eq./placeholder.jpg')
    .order('created_at', { ascending: false });
  
  if (error) {
    console.error('❌ Error fetching blog posts:', error);
    return [];
  }
  
  console.log(`📊 Found ${data.length} blog posts without images`);
  return data;
}

async function updateBlogPostImage(supabase, blogId, imageUrl) {
  console.log(`💾 Updating blog post ${blogId} with image URL...`);
  
  const { error } = await supabase
    .from(SUPABASE_CONFIG.tableName)
    .update({ 
      featured_image: imageUrl,
      updated_at: new Date().toISOString()
    })
    .eq('id', blogId);
  
  if (error) {
    console.error(`❌ Error updating blog post: ${error.message}`);
    return false;
  }
  
  console.log('✅ Blog post updated successfully');
  return true;
}

// ===== MAIN FUNCTIONS =====

function processBlogPostForMCP(blogPost) {
  console.log(`\n🔄 Processing blog post for MCP: "${blogPost.title}"`);
  
  const prompt = getImagePrompt(blogPost.title, blogPost.slug, blogPost.excerpt);
  const mcpRequest = generateMCPImageRequest(prompt);
  const filename = generateImageFilename(blogPost.slug);
  
  console.log(`📁 Generated filename: ${filename}`);
  console.log(`🎯 Blog post ID: ${blogPost.id}`);
  
  return {
    blogPost,
    prompt,
    mcpRequest,
    filename
  };
}

async function main() {
  console.log('🚀 Starting Blog Image Generator (MCP Version)...\n');
  
  const supabase = createClient(SUPABASE_CONFIG.url, SUPABASE_CONFIG.key);
  
  console.log('📋 Configuration:');
  console.log(`   - Replicate Model: ${REPLICATE_CONFIG.model}`);
  console.log(`   - R2 Endpoint: ${CLOUDFLARE_R2_CONFIG.endpoint}`);
  console.log(`   - Custom Domain: ${CLOUDFLARE_R2_CONFIG.publicUrlBase}`);
  console.log(`   - Blog Table: ${SUPABASE_CONFIG.tableName}`);
  
  const blogPosts = await getBlogPostsWithoutImages(supabase);
  
  if (blogPosts.length === 0) {
    console.log('\n✅ All blog posts already have featured images!');
    return;
  }
  
  console.log(`\n🎯 Found ${blogPosts.length} blog posts to process...`);
  console.log('\n💡 MCP Workflow:');
  console.log('   1. For each blog post below, use the MCP request to generate an image');
  console.log('   2. Upload the generated image to R2 using the provided filename');
  console.log('   3. Update the blog post with the custom domain URL');
  
  blogPosts.forEach((blogPost, index) => {
    const result = processBlogPostForMCP(blogPost);
    console.log(`\n--- Blog Post ${index + 1}/${blogPosts.length} ---`);
    console.log(`Title: "${result.blogPost.title}"`);
    console.log(`Slug: ${result.blogPost.slug}`);
    console.log(`Prompt: ${result.prompt}`);
    console.log(`Filename: ${result.filename}`);
    console.log('MCP Request:', JSON.stringify(result.mcpRequest, null, 2));
  });
  
  console.log('\n🎉 MCP processing plan generated!');
  console.log('\n📝 Next Steps:');
  console.log('   1. Use the MCP requests above to generate images');
  console.log('   2. Upload images to R2 with the specified filenames');
  console.log('   3. Update blog posts with URLs using custom domain');
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { 
  getImagePrompt, 
  generateMCPImageRequest, 
  processBlogPostForMCP,
  uploadToCloudflareR2,
  updateBlogPostImage
};
