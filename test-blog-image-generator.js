#!/usr/bin/env node

/**
 * Test Script for Blog Image Generator
 * 
 * This script tests the blog image generator functionality with sample data
 * to ensure Replicate Flux integration works correctly.
 */

const { generateImageForBlogPost, getImagePrompt, createImageWithReplicate } = require('./blog-image-generator.js');
const { createClient } = require('@supabase/supabase-js');

// Test configuration
const SUPABASE_CONFIG = {
  url: 'https://rhcwssrsdwakputbbpuf.supabase.co',
  key: 'sb_publishable_oIFU2557bCSsMHmplaGSeg_VkTmMr0K',
  tableName: 'findcaraccidentattorneys-blog'
};

// Sample blog post data for testing
const sampleBlogPost = {
  id: 'test-id',
  title: 'What to Do Immediately After a Car Accident',
  slug: 'what-to-do-immediately-after-car-accident',
  excerpt: 'Learn the essential steps to take immediately after a car accident to protect your health, safety, and legal rights. A comprehensive guide for accident victims.',
  content: 'When you are involved in a car accident, the first few minutes and hours are crucial...',
  author: 'Test Author',
  featured_image: null
};

async function testImagePromptGeneration() {
  console.log('🧪 Testing Image Prompt Generation...\n');
  
  const testCases = [
    {
      title: 'What to Do Immediately After a Car Accident',
      slug: 'what-to-do-immediately-after-car-accident',
      excerpt: 'Essential steps after a car accident'
    },
    {
      title: 'Understanding Personal Injury Claims',
      slug: 'understanding-personal-injury-claims-complete-guide',
      excerpt: 'A comprehensive guide to personal injury claims'
    },
    {
      title: 'How to Choose the Right Car Accident Attorney',
      slug: 'how-to-choose-right-car-accident-attorney',
      excerpt: 'Tips for selecting legal representation'
    },
    {
      title: 'Insurance Claims Process Explained',
      slug: 'insurance-claims-process-explained',
      excerpt: 'Understanding insurance coverage and claims'
    }
  ];

  testCases.forEach((testCase, index) => {
    const prompt = getImagePrompt(testCase.title, testCase.slug, testCase.excerpt);
    console.log(`Test ${index + 1}: "${testCase.title}"`);
    console.log(`Generated Prompt: ${prompt}`);
    console.log('---');
  });
}

async function testReplicateImageGeneration() {
  console.log('\n🎨 Testing Replicate Image Generation...\n');
  
  // Check if REPLICATE_API_TOKEN is set
  if (!process.env.REPLICATE_API_TOKEN) {
    console.log('⚠️  REPLICATE_API_TOKEN environment variable not set');
    console.log('   Please set it with: export REPLICATE_API_TOKEN=your_token_here');
    return false;
  }

  const testPrompt = getImagePrompt(sampleBlogPost.title, sampleBlogPost.slug, sampleBlogPost.excerpt);
  console.log(`Testing with prompt: "${testPrompt}"`);
  
  try {
    const imageUrl = await createImageWithReplicate(testPrompt);
    if (imageUrl) {
      console.log(`✅ Image generation successful!`);
      console.log(`🔗 Image URL: ${imageUrl}`);
      return true;
    } else {
      console.log('❌ Image generation failed - no URL returned');
      return false;
    }
  } catch (error) {
    console.log(`❌ Image generation failed: ${error.message}`);
    return false;
  }
}

async function testSupabaseConnection() {
  console.log('\n📊 Testing Supabase Connection...\n');
  
  try {
    const supabase = createClient(SUPABASE_CONFIG.url, SUPABASE_CONFIG.key);
    
    // Test connection by fetching one blog post
    const { data, error } = await supabase
      .from(SUPABASE_CONFIG.tableName)
      .select('id, title, slug, excerpt, featured_image')
      .limit(1);
    
    if (error) {
      console.log(`❌ Supabase connection failed: ${error.message}`);
      return false;
    }
    
    console.log(`✅ Supabase connection successful!`);
    console.log(`📋 Found ${data.length} blog post(s) in table`);
    
    if (data.length > 0) {
      console.log(`Sample post: "${data[0].title}"`);
    }
    
    return true;
  } catch (error) {
    console.log(`❌ Supabase connection error: ${error.message}`);
    return false;
  }
}

async function runFullTest() {
  console.log('🚀 Running Full Integration Test...\n');
  
  if (!process.env.REPLICATE_API_TOKEN) {
    console.log('⚠️  Skipping full test - REPLICATE_API_TOKEN not set');
    return;
  }

  try {
    const supabase = createClient(SUPABASE_CONFIG.url, SUPABASE_CONFIG.key);
    
    // Use sample blog post for testing
    console.log(`Testing with sample blog post: "${sampleBlogPost.title}"`);
    
    // Note: This would actually generate an image and upload to R2
    // Uncomment the line below to run the full test (be careful with API costs)
    // const success = await generateImageForBlogPost(supabase, sampleBlogPost);
    
    console.log('⚠️  Full test skipped to avoid API costs');
    console.log('   Uncomment the generateImageForBlogPost call in test script to run full test');
    
  } catch (error) {
    console.log(`❌ Full test failed: ${error.message}`);
  }
}

async function main() {
  console.log('🧪 Blog Image Generator Test Suite\n');
  console.log('=====================================\n');
  
  // Run individual tests
  await testImagePromptGeneration();
  await testSupabaseConnection();
  await testReplicateImageGeneration();
  await runFullTest();
  
  console.log('\n🎉 Test suite completed!');
  console.log('\n💡 To run the actual image generator:');
  console.log('   1. Set REPLICATE_API_TOKEN environment variable');
  console.log('   2. Run: node blog-image-generator.js');
}

// Run tests if this script is executed directly
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { testImagePromptGeneration, testReplicateImageGeneration, testSupabaseConnection };
