#!/usr/bin/env node

/**
 * Test Complete Workflow
 * 
 * This script tests the complete blog image generation workflow
 */

const { processOneBlogPost } = require('./complete-blog-image-workflow.js');
const { createClient } = require('@supabase/supabase-js');

const SUPABASE_CONFIG = {
  url: 'https://rhcwssrsdwakputbbpuf.supabase.co',
  key: 'sb_publishable_oIFU2557bCSsMHmplaGSeg_VkTmMr0K',
  tableName: 'findcaraccidentattorneys-blog'
};

const supabase = createClient(SUPABASE_CONFIG.url, SUPABASE_CONFIG.key);

// Pre-generated images from our MCP tests
const testImages = {
  'common-mistakes-avoid-after-car-accident': 'https://replicate.delivery/xezq/oHz5eXrL6a3GDKtpj4KZJH4StugKj4oRV2t4Uq3gIh1eU1RVA/out-0.webp',
  'what-to-do-immediately-after-car-accident': 'https://replicate.delivery/xezq/DYkksn9z0PIqGt26Yrl01YpHgmK1cLN9l7r0E0FhhPBdUdUF/out-0.webp',
  'how-to-choose-right-car-accident-attorney': 'https://replicate.delivery/xezq/lV2Ril8PviLqEBkmk7SUm2nw2usEnjG9MIjanN5GkbCoUdUF/out-0.webp'
};

async function processRemainingBlogPosts() {
  console.log('🚀 Processing Remaining Blog Posts\n');
  
  // Get remaining blog posts without images
  const { data: blogPosts, error } = await supabase
    .from(SUPABASE_CONFIG.tableName)
    .select('*')
    .or('featured_image.is.null,featured_image.eq./placeholder.jpg')
    .order('created_at', { ascending: false });
  
  if (error) {
    console.error('❌ Error fetching blog posts:', error);
    return;
  }
  
  console.log(`📊 Found ${blogPosts.length} blog posts to process\n`);
  
  let successCount = 0;
  let failureCount = 0;
  
  for (const blogPost of blogPosts) {
    console.log(`\n--- Processing: "${blogPost.title}" ---`);
    
    // Get appropriate test image based on slug
    let imageUrl = testImages[blogPost.slug];
    
    if (!imageUrl) {
      // Use a default image if we don't have a specific one
      imageUrl = testImages['what-to-do-immediately-after-car-accident'];
      console.log('⚠️  Using default test image');
    }
    
    console.log(`🎨 Using image: ${imageUrl.substring(0, 50)}...`);
    
    try {
      const success = await processOneBlogPost(blogPost, imageUrl);
      
      if (success) {
        successCount++;
        console.log('✅ Success!');
      } else {
        failureCount++;
        console.log('❌ Failed!');
      }
      
      // Small delay between requests
      await new Promise(resolve => setTimeout(resolve, 1000));
      
    } catch (error) {
      console.error(`❌ Error processing ${blogPost.title}: ${error.message}`);
      failureCount++;
    }
  }
  
  console.log('\n🎉 Processing Complete!');
  console.log(`📊 Results:`);
  console.log(`   ✅ Successful: ${successCount}`);
  console.log(`   ❌ Failed: ${failureCount}`);
  console.log(`   📝 Total: ${successCount + failureCount}`);
}

async function verifyAllBlogPosts() {
  console.log('\n🔍 Verifying All Blog Posts\n');
  
  const { data: allPosts, error } = await supabase
    .from(SUPABASE_CONFIG.tableName)
    .select('id, title, featured_image')
    .order('created_at', { ascending: false });
  
  if (error) {
    console.error('❌ Error fetching blog posts:', error);
    return;
  }
  
  console.log('📋 Blog Posts Status:');
  
  let withImages = 0;
  let withoutImages = 0;
  
  allPosts.forEach((post, index) => {
    const hasImage = post.featured_image && 
                    post.featured_image !== '/placeholder.jpg' && 
                    post.featured_image.includes('images.findcaraccidentlawyers.org');
    
    console.log(`${index + 1}. "${post.title}"`);
    
    if (hasImage) {
      console.log(`   ✅ Has image: ${post.featured_image}`);
      withImages++;
    } else {
      console.log(`   ❌ No image: ${post.featured_image || 'null'}`);
      withoutImages++;
    }
  });
  
  console.log(`\n📊 Summary:`);
  console.log(`   ✅ With images: ${withImages}`);
  console.log(`   ❌ Without images: ${withoutImages}`);
  console.log(`   📝 Total posts: ${allPosts.length}`);
  
  if (withoutImages === 0) {
    console.log('\n🎉 All blog posts now have featured images!');
  }
}

async function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--verify')) {
    await verifyAllBlogPosts();
  } else if (args.includes('--process')) {
    await processRemainingBlogPosts();
    await verifyAllBlogPosts();
  } else {
    console.log('🧪 Test Complete Workflow\n');
    console.log('Usage:');
    console.log('  node test-complete-workflow.js --process  # Process remaining blog posts');
    console.log('  node test-complete-workflow.js --verify   # Verify all blog posts');
    console.log('\nThis script will:');
    console.log('✅ Process remaining blog posts with pre-generated images');
    console.log('✅ Upload images to R2 with custom domain URLs');
    console.log('✅ Update Supabase database');
    console.log('✅ Verify all posts have images');
  }
}

if (require.main === module) {
  main().catch(console.error);
}
