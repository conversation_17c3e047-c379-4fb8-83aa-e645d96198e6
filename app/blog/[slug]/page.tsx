import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import { Head<PERSON> } from "@/components/header"
import { Footer } from "@/components/footer"
import { Breadcrumb } from "@/components/breadcrumb"
import { RelatedItems } from "@/components/related-items"
import { Badge } from "@/components/ui/badge"
import { Calendar, User, Clock, ArrowLeft } from "lucide-react"
import { getBlogBySlug, getRelatedBlogs } from "@/lib/supabase"
import { addInternalLinks, renderContentWithLinks } from "@/lib/utils/internal-links"
import Link from "next/link"
import Image from "next/image"
import { Button } from "@/components/ui/button"

interface BlogPostPageProps {
  params: Promise<{
    slug: string
  }>
}

export async function generateMetadata({ params }: BlogPostPageProps): Promise<Metadata> {
  const { slug } = await params
  const { blog } = await getBlogBySlug(slug)

  if (!blog) {
    return {
      title: 'Blog Post Not Found',
      description: 'The requested blog post could not be found.',
    }
  }

  return {
    title: blog.meta_title || blog.title,
    description: blog.meta_description || blog.excerpt || `Read ${blog.title} on our legal blog.`,
    alternates: {
      canonical: `/blog/${blog.slug}`,
    },
  }
}

export default async function BlogPostPage({ params }: BlogPostPageProps) {
  const { slug } = await params
  const { blog, error } = await getBlogBySlug(slug)

  if (error || !blog) {
    notFound()
  }

  // Get related blogs based on tags
  const { blogs: relatedBlogs } = await getRelatedBlogs(blog.id, blog.tags, 3)

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const estimateReadTime = (content: string) => {
    const wordsPerMinute = 200
    const wordCount = content.split(' ').length
    return Math.ceil(wordCount / wordsPerMinute)
  }

  const breadcrumbs = [
    { name: 'Home', href: '/' },
    { name: 'Blog', href: '/blog' },
    { name: blog.title, href: `/blog/${blog.slug}` }
  ]

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <main className="container mx-auto px-4 py-8">
        <Breadcrumb items={breadcrumbs} />
        
        {/* Back to Blog Button */}
        <div className="mb-6">
          <Link href="/blog">
            <Button variant="outline" className="flex items-center gap-2">
              <ArrowLeft className="h-4 w-4" />
              Back to Blog
            </Button>
          </Link>
        </div>

        <article className="max-w-4xl mx-auto">
          {/* Featured Image */}
          {blog.featured_image && (
            <div className="relative h-64 md:h-96 w-full overflow-hidden rounded-lg mb-8">
              <Image
                src={blog.featured_image}
                alt={blog.title}
                fill
                className="object-cover"
                priority
              />
            </div>
          )}

          {/* Article Header */}
          <header className="mb-8">
            <h1 className="text-4xl md:text-5xl font-bold mb-4">{blog.title}</h1>
            
            {blog.excerpt && (
              <p className="text-xl text-muted-foreground mb-6">{blog.excerpt}</p>
            )}

            <div className="flex flex-wrap items-center gap-4 text-sm text-muted-foreground mb-6">
              <div className="flex items-center gap-2">
                <User className="h-4 w-4" />
                <span>{blog.author}</span>
              </div>
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                <span>{formatDate(blog.published_date)}</span>
              </div>
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4" />
                <span>{estimateReadTime(blog.content)} min read</span>
              </div>
            </div>

            {/* Tags */}
            <div className="flex flex-wrap gap-2">
              {blog.tags.map((tag) => (
                <Link key={tag} href={`/blog?tag=${tag}`}>
                  <Badge variant="secondary" className="hover:bg-primary hover:text-primary-foreground transition-colors">
                    {tag.replace('-', ' ')}
                  </Badge>
                </Link>
              ))}
            </div>
          </header>

          {/* Article Content */}
          <div className="prose prose-xl max-w-none mb-12 text-lg leading-relaxed">
            {await Promise.all(blog.content.split('\n').map(async (paragraph, index) => {
              if (paragraph.trim() === '') return null

              if (paragraph.startsWith('## ')) {
                return (
                  <h2 key={index} className="text-2xl font-semibold mt-8 mb-4">
                    {paragraph.replace('## ', '')}
                  </h2>
                )
              }

              if (paragraph.startsWith('### ')) {
                return (
                  <h3 key={index} className="text-xl font-semibold mt-6 mb-3">
                    {paragraph.replace('### ', '')}
                  </h3>
                )
              }

              // Add internal links to paragraph content
              const processedParagraph = await addInternalLinks(paragraph, blog.slug)

              return (
                <p
                  key={index}
                  className="mb-4 leading-relaxed text-lg"
                  dangerouslySetInnerHTML={renderContentWithLinks(processedParagraph)}
                />
              )
            }))}
          </div>
        </article>

        {/* Related Articles */}
        <RelatedItems
          title="You may be interested in"
          items={relatedBlogs}
          type="blogs"
        />
      </main>
      
      <Footer />
    </div>
  )
}
